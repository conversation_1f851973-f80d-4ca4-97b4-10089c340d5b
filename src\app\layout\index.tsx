import { Box } from '@mui/material'
import { Outlet } from 'react-router-dom'
import Header from './Header'

export default function RootLayout() {
    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', overflow: 'hidden' }}>
            <Header />
            <Box component="main" sx={{ py: { xs: 1, md: 2 }, px: { xs: 1, sm: 2, md: 3 }, flexGrow: 1, width: '100%', overflow: 'auto' }}>
                <Outlet />
            </Box>
        </Box>
    )
}
