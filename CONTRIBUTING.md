# Contributing

## Branching
- `main`: protected, release-ready
- feature branches: `feat/<scope>-<short-desc>`
- fix branches: `fix/<scope>-<short-desc>`

## Commits
- Use clear, imperative messages (e.g., "add settings import button")
- Group related changes; keep commits small and focused

## Pull Requests
- One feature/fix per PR
- Include description, screenshots (UI), and testing notes
- Ensure CI/build passes, no lint/type errors

## Code Style
- TypeScript strictness; avoid `any`
- Use meaningful names, early returns, shallow nesting
- Prettier + EditorConfig govern formatting
- ESLint for lint rules

## Testing
- Add unit/integration tests where applicable
- Ensure `npm run typecheck` and `npm run lint` pass

## Pre-commit
- Husky + lint-staged enforce lint/format on staged files

## Documentation
- Update README/SETUP if workflows change
- Document new env vars in `src/core/config/env.ts` and README
