# Setup

## Prerequisites
- Node.js 18+ (L<PERSON> recommended)
- npm 10+
- Git (for <PERSON><PERSON> hooks)

## Install
```
npm install
```

## Development
```
npm run dev
```
- MSW mock server starts automatically in development (see `src/mocks/`).
- Open http://localhost:5173

## Build & Preview
```
npm run build
npm run preview
```

## Linting & Formatting
```
npm run lint
npm run typecheck
npm run format
```

## Pre-commit Hooks (Husky)
- Ensure Git is installed.
- On first install, <PERSON><PERSON> is initialized via `npm run prepare`.
- Pre-commit runs ESLint/Prettier on staged files via lint-staged.

## Windows Notes
- Husky hooks are POSIX sh scripts; Git Bash is used automatically by Git on Windows.
- PowerShell commands in package scripts are platform-agnostic.

## macOS Notes
- Use Homebrew to install Node if needed: `brew install node`

## Environment Variables
- See `src/core/config/env.ts`. Add variables as `VITE_*` in `.env`.
