/**
 * Tests for DashboardPage component
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '../../test-utils'
import DashboardPage from '../DashboardPage'
import { mockTasks } from '../../__mocks__/mockData'

// Mock the tasks service
jest.mock('../../app/dashboard/services/tasks', () => ({
	fetchTasks: jest.fn(),
}))

// Mock the ElementCard component
jest.mock('../../app/dashboard', () => ({
	ElementCard: ({
		tasks,
		className,
		'data-item-id': dataItemId,
		ref
	}: {
		tasks: Array<unknown>;
		className?: string;
		'data-item-id': string;
		ref?: React.Ref<HTMLDivElement>
	}) => (
		<div
			data-testid={`element-card-${dataItemId}`}
			className={className}
			data-item-id={dataItemId}
			ref={ref}
		>
			Element Card {dataItemId} with {tasks.length} tasks
		</div>
	),
}))

// Mock the useTheme hook
jest.mock('@mui/material', () => ({
	...jest.requireActual('@mui/material'),
	useTheme: () => ({
		palette: {
			background: { paper: '#ffffff' },
			divider: '#e0e0e0',
			mode: 'light',
		},
		spacing: (units: number) => `${units * 8}px`,
	}),
}))

import { fetchTasks } from '../../app/dashboard/services/tasks'

const mockFetchTasks = fetchTasks as jest.MockedFunction<typeof fetchTasks>

describe('DashboardPage Component', () => {
	beforeEach(() => {
		jest.clearAllMocks()
		// Reset matchMedia mock
		Object.defineProperty(window, 'matchMedia', {
			writable: true,
			value: jest.fn().mockImplementation(() => ({
				matches: false, // Portrait by default
				media: '(orientation: landscape)',
				onchange: null,
				addListener: jest.fn(),
				removeListener: jest.fn(),
				addEventListener: jest.fn(),
				removeEventListener: jest.fn(),
				dispatchEvent: jest.fn(),
			})),
		})
	})

	describe('initial loading state', () => {
		it('should show loading message initially', () => {
			// Arrange
			mockFetchTasks.mockImplementation(() => new Promise(() => { })) // Never resolves

			// Act
			render(<DashboardPage />)

			// Assert
			expect(screen.getByText('Loading…')).toBeInTheDocument()
		})
	})

	describe('when tasks are loaded successfully', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should display tasks after loading', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Should show 4 element cards in portrait mode
			expect(screen.getByTestId('element-card-0')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-1')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-2')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-3')).toBeInTheDocument()
		})

		it('should display pagination', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Should show pagination with 3 pages (12 total elements / 4 per page)
			expect(screen.getByRole('navigation')).toBeInTheDocument()
			expect(screen.getByText('1')).toBeInTheDocument()
			expect(screen.getByText('2')).toBeInTheDocument()
			expect(screen.getByText('3')).toBeInTheDocument()
		})

		it('should display correct number of tasks per element card', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Each element card should show the number of tasks
			expect(screen.getByText('Element Card 0 with 3 tasks')).toBeInTheDocument()
			expect(screen.getByText('Element Card 1 with 3 tasks')).toBeInTheDocument()
		})
	})

	describe('error handling', () => {
		it('should display error message when fetch fails', async () => {
			// Arrange
			const errorMessage = 'Failed to fetch tasks'
			mockFetchTasks.mockRejectedValue(new Error(errorMessage))

			// Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.getByText(errorMessage)).toBeInTheDocument()
			})
			expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
		})

		it('should not display error for abort errors', async () => {
			// Arrange
			const abortError = new DOMException('Aborted', 'AbortError')
			mockFetchTasks.mockRejectedValue(abortError)

			// Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})
			expect(screen.queryByText('Aborted')).not.toBeInTheDocument()
		})
	})

	describe('pagination functionality', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should change page when pagination is clicked', async () => {
			// Arrange
			render(<DashboardPage />)

			// Wait for loading to complete
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Act
			const page2Button = screen.getByText('2')
			fireEvent.click(page2Button)

			// Assert
			await waitFor(() => {
				expect(screen.getByTestId('element-card-4')).toBeInTheDocument()
				expect(screen.getByTestId('element-card-5')).toBeInTheDocument()
				expect(screen.getByTestId('element-card-6')).toBeInTheDocument()
				expect(screen.getByTestId('element-card-7')).toBeInTheDocument()
			})
		})

		it('should show correct page items for different pages', async () => {
			// Arrange
			render(<DashboardPage />)

			// Wait for loading to complete
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Page 1 should show items 0-3
			expect(screen.getByTestId('element-card-0')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-3')).toBeInTheDocument()

			// Go to page 2
			const page2Button = screen.getByText('2')
			fireEvent.click(page2Button)

			// Page 2 should show items 4-7
			await waitFor(() => {
				expect(screen.getByTestId('element-card-4')).toBeInTheDocument()
				expect(screen.getByTestId('element-card-7')).toBeInTheDocument()
			})
		})
	})

	describe('orientation handling', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should show 4 elements per page in portrait mode', async () => {
			// Arrange
			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false, // Portrait
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: jest.fn(),
					removeEventListener: jest.fn(),
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Should show 4 element cards
			expect(screen.getByTestId('element-card-0')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-1')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-2')).toBeInTheDocument()
			expect(screen.getByTestId('element-card-3')).toBeInTheDocument()
		})

		it('should show 1 element per page in landscape mode', async () => {
			// Arrange
			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: true, // Landscape
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: jest.fn(),
					removeEventListener: jest.fn(),
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Should show only 1 element card
			expect(screen.getByTestId('element-card-0')).toBeInTheDocument()
			expect(screen.queryByTestId('element-card-1')).not.toBeInTheDocument()
		})
	})

	describe('sticky header', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should have sticky pagination header', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			const paginationContainer = screen.getByRole('navigation').closest('.sticky')
			expect(paginationContainer).toHaveClass('sticky', 'top-0', 'z-10')
		})
	})

	describe('data refresh', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should refresh data periodically when items are visible', async () => {
			// Arrange
			jest.useFakeTimers()
			render(<DashboardPage />)

			// Wait for loading to complete
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			// Act - Fast forward 10 seconds
			act(() => {
				jest.advanceTimersByTime(10000)
			})

			// Assert - Should call fetchTasks again
			expect(mockFetchTasks).toHaveBeenCalledTimes(2) // Initial + periodic

			// Cleanup
			jest.useRealTimers()
		})
	})

	describe('accessibility', () => {
		beforeEach(() => {
			mockFetchTasks.mockResolvedValue(mockTasks)
		})

		it('should have proper navigation structure', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			expect(screen.getByRole('navigation')).toBeInTheDocument()
		})

		it('should have proper data attributes for intersection observer', async () => {
			// Arrange & Act
			render(<DashboardPage />)

			// Assert
			await waitFor(() => {
				expect(screen.queryByText('Loading…')).not.toBeInTheDocument()
			})

			expect(screen.getByTestId('element-card-0')).toHaveAttribute('data-item-id', '0')
			expect(screen.getByTestId('element-card-1')).toHaveAttribute('data-item-id', '1')
		})
	})
})
