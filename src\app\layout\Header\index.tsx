import { A<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, IconButton } from '@mui/material'
import { Link as RouterLink } from 'react-router-dom'
import { useAppStore } from '../../../core/store'
import { useTranslation } from 'react-i18next'
import LightModeIcon from '@mui/icons-material/LightMode'
import DarkModeIcon from '@mui/icons-material/DarkMode'

export default function Header() {
    const { t } = useTranslation()
    const themeMode = useAppStore((s) => s.themeMode)
    const toggleThemeMode = useAppStore((s) => s.toggleThemeMode)

    return (
        <AppBar position="sticky" sx={{ top: 0 }}>
            <Toolbar variant="dense">
                <Typography variant="h6" sx={{ flexGrow: 1, fontSize: 16 }}>
                    VMApp
                </Typography>
                <Button
                    component={RouterLink}
                    to="/"
                    color="inherit"
                >
                    {t('nav.dashboard')}
                </Button>
                <Button
                    component={RouterLink}
                    to="/settings"
                    color="inherit"
                >
                    {t('nav.settings')}
                </Button>
                <IconButton
                    color="inherit"
                    onClick={toggleThemeMode}
                    sx={{ ml: 1 }}
                    aria-label="toggle theme"
                >
                    {themeMode === 'light' ? <DarkModeIcon fontSize="small" /> : <LightModeIcon fontSize="small" />}
                </IconButton>
            </Toolbar>
        </AppBar>
    )
}
